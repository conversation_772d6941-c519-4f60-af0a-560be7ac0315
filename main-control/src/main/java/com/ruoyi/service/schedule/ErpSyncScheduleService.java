package com.ruoyi.service.schedule;

import com.ruoyi.common.exception.CustomException;
import com.ruoyi.service.basicData.*;
import com.ruoyi.service.bill.*;
import com.ruoyi.service.sys.ProjectSysConfigService;
import com.ruoyi.utils.constant.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * ERP同步定时任务服务
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class ErpSyncScheduleService {

    @Resource
    private ProjectSysConfigService projectSysConfigService;

    // 执行状态锁定，防止重复执行
    private final AtomicBoolean basicDataSyncRunning = new AtomicBoolean(false);
    private final AtomicBoolean billDataSyncRunning = new AtomicBoolean(false);

    // 记录最后执行时间和状态
    private volatile String lastBasicDataSyncTime = "未执行";
    private volatile String lastBillDataSyncTime = "未执行";
    private volatile String lastBasicDataSyncStatus = "未知";
    private volatile String lastBillDataSyncStatus = "未知";

    // 基础数据同步服务
    @Resource
    private BasicCompanyInfoService basicCompanyInfoService;
    @Resource
    private BasicCompanyGroupService basicCompanyGroupService;
    @Resource
    private BasicContactInfoService basicContactInfoService;
    @Resource
    private BasicMaterialClassifyService basicMaterialClassifyService;
    @Resource
    private BasicUnitInfoService basicUnitInfoService;

    // ERP单据同步服务
    @Resource
    private DeliveryNoticeService deliveryNoticeService;
    @Resource
    private ReceiveNoticeService receiveNoticeService;
    @Resource
    private ReturnMaterialService returnMaterialService;
    @Resource
    private ReturnNoticeService returnNoticeService;
    @Resource
    private SimpleProductionPickingMaterialService simpleProductionPickingMaterialService;
    @Resource
    private SimpleProductionInStockService simpleProductionInStockService;
    @Resource
    private ProductionInStockService productionInStockService;

    /**
     * 服务启动后延迟执行初始同步
     * 延迟2分钟执行，避免启动时系统资源紧张
     */
    @PostConstruct
    public void initSync() {
        log.info("ERP同步定时任务服务已启动，将在2分钟后执行初始同步");

        // 使用新线程延迟执行，避免阻塞启动过程
        new Thread(() -> {
            try {
                Thread.sleep(2 * 60 * 1000); // 延迟2分钟
                log.info("开始执行启动后的初始同步");

                // 执行初始同步
                syncBasicDataFromErp();
                Thread.sleep(30 * 1000); // 间隔30秒
                syncBillDataFromErp();

                log.info("启动后的初始同步完成");
            } catch (InterruptedException e) {
                log.info("初始同步被中断: {}", e.getMessage());
                Thread.currentThread().interrupt();
            } catch (CustomException e) {
                log.warn("初始同步执行失败: {}", e.getMessage());
            }
            catch (Exception e) {
                log.info("初始同步执行失败: {}", e.getMessage());
            }
        }, "ERP-InitSync-Thread").start();
    }

    /**
     * 基础数据批量同步定时任务
     * 每20分钟执行一次
     */
    @Scheduled(cron = "0 */20 * * * ?")
    public void syncBasicDataFromErp() {
        // 检查是否正在执行
        if (!basicDataSyncRunning.compareAndSet(false, true)) {
            log.warn("基础数据同步正在执行中，跳过本次调度");
            throw new CustomException("基础数据同步正在执行中，跳过本次调度");
        }

        try {
            // 检查基础数据同步开关
            String syncEnabled = projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.ERP_BASIC_DATA_SYNC_ENABLED);
            if (!"true".equals(syncEnabled)) {
                log.debug("基础数据同步已禁用，跳过执行");
                throw new CustomException("基础数据同步已禁用，跳过执行");
            }

            executeBasicDataSync();
        } finally {
            // 确保释放锁定状态
            basicDataSyncRunning.set(false);
        }
    }

    /**
     * 执行基础数据同步的具体逻辑
     */
    private void executeBasicDataSync() {

        log.info("=== 开始执行基础数据批量同步任务 ===");
        long startTime = System.currentTimeMillis();

        try {
            lastBasicDataSyncTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            lastBasicDataSyncStatus = "执行中";

        // 1. 同步客户分组
        syncWithErrorHandling("客户分组", () -> {
            basicCompanyGroupService.syncFromErpCustomerGroups();
        });

        // 2. 同步供应商分组
        syncWithErrorHandling("供应商分组", () -> {
            basicCompanyGroupService.syncFromErpSupplierGroups();
        });

        // 3. 同步客户信息
        syncWithErrorHandling("客户信息", () -> {
            basicCompanyInfoService.syncFromErpCustomers();
        });

        // 4. 同步供应商信息
        syncWithErrorHandling("供应商信息", () -> {
            basicCompanyInfoService.syncFromErpSuppliers();
        });

        // 5. 同步联系人信息
        syncWithErrorHandling("联系人信息", () -> {
            basicContactInfoService.syncFromErp();
        });

        // 6. 同步物料分类信息
        syncWithErrorHandling("物料分类信息", () -> {
            basicMaterialClassifyService.syncFromErpGroups();
        });

        // 7. 同步单位信息
        syncWithErrorHandling("单位信息", () -> {
            basicUnitInfoService.syncFromErp();
        });

            long endTime = System.currentTimeMillis();
            lastBasicDataSyncStatus = "成功";
            log.info("=== 基础数据批量同步任务完成，耗时：{}ms ===", (endTime - startTime));
        } catch (Exception e) {
            lastBasicDataSyncStatus = "失败: " + e.getMessage();
            log.error("基础数据批量同步任务执行失败", e);
            throw e;
        }
    }

    /**
     * ERP单据批量同步定时任务
     * 每5分钟执行一次
     */
    @Scheduled(cron = "0 */5 * * * ?")
    public void syncBillDataFromErp() {
        // 检查是否正在执行
        if (!billDataSyncRunning.compareAndSet(false, true)) {
            log.warn("ERP单据同步正在执行中，跳过本次调度");
            throw new CustomException("ERP单据同步正在执行中，跳过本次调度");
        }

        try {
            // 检查单据同步开关
            String syncEnabled = projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.ERP_BILL_SYNC_ENABLED);
            if (!"true".equals(syncEnabled)) {
                log.debug("ERP单据同步已禁用，跳过执行");
               throw new CustomException("ERP单据同步已禁用，跳过执行");
            }

            executeBillDataSync();
        } catch (CustomException e) {
            log.warn("ERP单据同步跳过: {}", e.getMessage());
        }finally {
            // 确保释放锁定状态
            billDataSyncRunning.set(false);
        }
    }

    /**
     * 执行ERP单据同步的具体逻辑
     */
    private void executeBillDataSync() {
        log.info("=== 开始执行ERP单据批量同步任务 ===");
        long startTime = System.currentTimeMillis();

        try {
            lastBillDataSyncTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            lastBillDataSyncStatus = "执行中";

        // 1. 同步发货通知单
        syncWithErrorHandling("发货通知单", () -> {
            deliveryNoticeService.syncFromErp();
        });

        // 2. 同步收料通知单
        syncWithErrorHandling("收料通知单", () -> {
            receiveNoticeService.syncFromErp();
        });

        // 3. 同步退料申请单
        syncWithErrorHandling("退料申请单", () -> {
            returnMaterialService.syncFromErp();
        });

        // 4. 同步退货通知单
        syncWithErrorHandling("退货通知单", () -> {
            returnNoticeService.syncFromErp();
        });

        // 5. 同步简单生产领料单
        syncWithErrorHandling("简单生产领料单", () -> {
            simpleProductionPickingMaterialService.syncFromErp();
        });

        // 6. 同步简单生产入库单
        syncWithErrorHandling("简单生产入库单", () -> {
            simpleProductionInStockService.syncFromErp();
        });

        // 7. 同步生产入库单
        syncWithErrorHandling("生产入库单", () -> {
            productionInStockService.syncFromErp();
        });

            long endTime = System.currentTimeMillis();
            lastBillDataSyncStatus = "成功";
            log.info("=== ERP单据批量同步任务完成，耗时：{}ms ===", (endTime - startTime));
        } catch (CustomException e) {
            log.warn("ERP单据同步跳过: {}", e.getMessage());
        }
        catch (Exception e) {
            lastBillDataSyncStatus = "失败: " + e.getMessage();
            log.error("ERP单据批量同步任务执行失败", e);
            throw e;
        }
    }

    /**
     * 带异常处理的同步执行方法
     * 
     * @param syncName 同步名称
     * @param syncAction 同步动作
     */
    private void syncWithErrorHandling(String syncName, SyncAction syncAction) {
        try {
            log.info("开始同步{}", syncName);
            long startTime = System.currentTimeMillis();
            
            syncAction.execute();
            
            long endTime = System.currentTimeMillis();
            log.info("{}同步完成，耗时：{}ms", syncName, (endTime - startTime));
        } catch (Exception e) {
            log.error("{}同步失败：{}", syncName, e.getMessage(), e);
        }
    }

    /**
     * 获取同步状态信息
     *
     * @return 同步状态详情
     */
    public Map<String, Object> getSyncStatus() {
        Map<String, Object> status = new HashMap<>();

        // 基础数据同步状态
        Map<String, Object> basicDataStatus = new HashMap<>();
        basicDataStatus.put("isRunning", basicDataSyncRunning.get());
        basicDataStatus.put("lastSyncTime", lastBasicDataSyncTime);
        basicDataStatus.put("lastSyncStatus", lastBasicDataSyncStatus);
        basicDataStatus.put("syncInterval", "每20分钟执行一次");

        // ERP单据同步状态
        Map<String, Object> billDataStatus = new HashMap<>();
        billDataStatus.put("isRunning", billDataSyncRunning.get());
        billDataStatus.put("lastSyncTime", lastBillDataSyncTime);
        billDataStatus.put("lastSyncStatus", lastBillDataSyncStatus);
        billDataStatus.put("syncInterval", "每5分钟执行一次");

        // 配置开关状态
        String basicDataEnabled = projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.ERP_BASIC_DATA_SYNC_ENABLED);
        String billDataEnabled = projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.ERP_BILL_SYNC_ENABLED);

        Map<String, Object> configStatus = new HashMap<>();
        configStatus.put("basicDataSyncEnabled", "true".equals(basicDataEnabled));
        configStatus.put("billDataSyncEnabled", "true".equals(billDataEnabled));

        status.put("basicDataSync", basicDataStatus);
        status.put("billDataSync", billDataStatus);
        status.put("config", configStatus);
        status.put("currentTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        return status;
    }

    /**
     * 同步动作函数式接口
     */
    @FunctionalInterface
    private interface SyncAction {
        void execute() throws Exception;
    }
}
