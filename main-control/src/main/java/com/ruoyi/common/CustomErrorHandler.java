package com.ruoyi.common;

import com.ruoyi.common.exception.CustomException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.ErrorHandler;
/**
 * 自定义定时任务错误处理器
 * 用于控制Spring定时任务异常的日志输出格式
 */
@Component
public class CustomErrorHandler implements ErrorHandler {
    
    private static final Logger log = LoggerFactory.getLogger(CustomErrorHandler.class);
    
    @Override
    public void handleError(Throwable throwable) {
        // 解析真实的异常
        Throwable rootCause = throwable;
        while (rootCause.getCause() != null) {
            rootCause = rootCause.getCause();
        }
        
        if (rootCause instanceof CustomException) {
            // 对于自定义异常，只记录简短信息
            log.warn("定时任务跳过: {}", rootCause.getMessage());
        } else {
            // 其他异常仍然记录完整堆栈
            log.error("定时任务执行异常", throwable);
        }
    }
}