package com.ruoyi.utils.constant;

/**
 * <AUTHOR>
 * @version v1.0
 * @Description: [通用常量类]
 * @date 2024/5/7 14:52
 */
public class CommonConstant {

    /**
     * Redis缓存标记
     */
    public static class RedisKey {

    }

    /**
     * BOM状态
     * 0：测试版本，1：正式版本
     */
    public static class BomState {
        public static final int TEST_VERSION = 0;
        public static final int PRODUCE_VERSION = 1;
    }

    /**
     * 单据来源
     * 0页面，1MES，2ERP
     */
    public static class BusinessSource {
        public static final int WEB = 0;
        public static final int MES = 1;
        public static final int ERP = 2;
    }

    /**
     * 单据是否锁定
     * 0未锁定，1已锁定
     */
    public static class IsLock {
        public static final int UN_LOCK = 0;
        public static final int LOCK = 1;
    }

    /**
     * 单据类型
     * 1:生产领料 2:生产补料 3:生产入库（成品入库） 4:生产退料 5采购入库  6采购退货出库  7销售出库  8销售退货入库 9物料调拨 10库存盘点 11仓库用料
     */

    public static class BoundType{
        // 生产领料单
        public static Integer SCLL = 1;
        // 生产补料单
        public static Integer SCBL = 2;
        // 生产入库（成品入库）单
        public static Integer SCRK = 3;
        // 生产退料单
        public static Integer SCTL = 4;
        // 采购入库单
        public static Integer CGRK = 5;
        // 采购退货单
        public static Integer CGTH = 6;
        // 销售出库单
        public static Integer XSCK = 7;
        // 销售退货入库单
        public static Integer XSTH = 8;
        // 物料调拨单
        public static Integer WLDB = 9;
        // 库存盘点单
        public static Integer KCPD = 10;
        // 仓库用料单
        public static Integer CKYL = 11;
    }

    /**
     * 单据状态
     */
    public static class BoundStatus{
        //未送审
        public static Integer PENDING_RE = 0;
        //审核中
        public static Integer UNDER_REVIEW = 1;
        //锁单
        public static Integer LOCKED = 2;
    }

    /**
     * 单据状态
     * 0未完成，1已完成
     */
    public static class DocumentStatus {
        public static final int UN_FINISH = 0;
        public static final int FINISH = 1;
    }


    /**
     * 立库名
     */
    public static class LkName{
        public static String BOX = "料框立库";

        public static String PLATE = "板材立库";

        public static String PROFILE = "型材立库";
    }

    /**
     * 信号类型
     */
    public static class SignalType{
        public static String MES_RECEIVE = "MES任务接收";

        public static String MES_RK_SEND = "MES入库下发";

        public static String MES_CK_SEND = "MES出库下发";

        public static String WMS_REPORT_MES = "MES报工";

        public static String PLATE = "板材立库";

        public static String PROFILE = "型材立库";
    }

    /**
     * 单据出入库类型
     */
    public static class InoutType{
        //入库
        public static Integer IN = 0;
        //出库
        public static Integer OUT = 1;
    }

    /**
     * 单据状态
     * 0待确认，1已确认
     */
    public static class DocumentState{
        public static Integer WAIT_CONFIRM = 0;
        public static Integer CONFIRM = 1;
    }

    /**
     * 是否页面下发
     */
    public static class IsWeb{
        public static Integer AUTO = 0;

        public static Integer WEB = 1;
    }



    /**
     * 系统配置项
     */
    public static class SysConfig{


        //共享文件夹\ftp(IP)
        public static final String SHARE_FILE_DICT_TYPE_IP = "share_ip";
        //共享文件夹\ftp(端口)
        public static final String SHARE_FILE_DICT_TYPE_PORT = "share_port";
        //共享文件夹\ftp(账号)
        public static final String SHARE_FILE_DICT_TYPE_NAME = "share_user_name";
        //共享文件夹\ftp(密码)
        public static final String SHARE_FILE_DICT_TYPE_PWD = "share_password";

    }

    /**
     * 容器状态
     */
    public static class ContainerState{
        //使用
        public static Integer USE = 0;
        //不使用
        public static Integer UN_USE = 1;
    }

    /**
     * 库位状态
     */
    public static class LocationState{
        //使用
        public static Integer USE = 0;
        //不使用
        public static Integer UN_USE = 1;
    }


    /**
     * 物料分类
     */
    public static class MaterialType{
        //板材
        public static Integer PLATE = 0;
        //型材
        public static Integer PROFILE = 1;
        //标准件
        public static Integer STANDARD = 2;
    }


    public static class TaskPrefix {
        //质检项前缀
        public static final String QC_ITEM_NO_HEAD = "ITEM_";
        //质检模板前缀
        public static final String QC_TEMPLATE_NO_HEAD = "QCT_";
        //质检编码前缀
        public static final String QC_IOC_NO_HEAD = "IQC_";

    }

    /**
     * 编码前缀
     */
    public static class CodePrefix{
        //仓库编码前缀
        public static String WAREHOUSE_PREFIX = "CK";
        //库存盘点单
        public static String INVENTORY_PREFIX = "KCPD";
        //仓库库位编码前缀
        public static String LOCATION_PREFIX = "KW";
        //仓库容器编码前缀
        public static String CONTAINER_PREFIX = "RQ";
        //物料进出记录前缀
        public static String MATERIAL_INOUT_PREFIX = "WLCRK";
        //仓库用料单前缀
        public static String WAREHOUSE_OUTBOUND_PREFIX = "CKYL";
        //物料调拨单前缀
        public static String MATERIAL_ALLOT_PREFIX = "WLDB";

    }


    /**
     * 物料分类 0 原材料，1半成品，2 成品，3其它
     */
    public class MaterialSort {
        public static final int ROW = 0;
        public static final int SEMI = 1;
        public static final int PRODUCT = 2;
        public static final int OTHER = 3;
    }
    /**
     * 物料是否外购
     */
    public static class IsOutSource {
        public static final int SELF_PRODUCED = 0; // 自产
        public static final int PURCHASED = 1;     // 外购
        public static final int EITHER = 2;        // 均可
    }
    /**
     * 报障任务进度 0进行 1完成 2延期
     */
    public static class DevFaultTaskStatus{
        public static final Integer RUN = 0;
        public static final Integer FINISH = 1;
        public static final Integer DELAY = 2;
    }

    /**
     * ERP上报状态
     * 0待上报，1上报成功，2上报失败
     */
    public static class ReportStatus{
        public static final Integer PENDING = 0;
        public static final Integer SUCCESS = 1;
        public static final Integer FAILED = 2;
    }



    /**
     * 项目配置项
     */
    public static class ProjectSysConfig {
        public static final String PRODUCT_CODE = "product_code";
        //ERP登录DBid()
        public static final String DB_ID = "erp_db_id";
        //ERP登录用户名()
        public static final String USER_NAME = "erp_user_name";
        //ERP登录密码()
        public static final String PASSWORD = "erp_password";
        //ERP服务器URL
        public static final String ERP_URL = "erp_url";
        //Logo路径
        public static final String LOGO_PATH = "logo_path";
        //通用-库存组织
        public static final String COMMON_STOCK_ORG = "common_stock_org";
        //通用-货主类型
        public static final String COMMON_OWNER_TYPE = "common_owner_type";
        //通用-货主
        public static final String COMMON_OWNER = "common_owner";
        //通用-保管者类型
        public static final String COMMON_KEEPER_TYPE = "common_keeper_type";
        //通用-保管者
        public static final String COMMON_KEEPER = "common_keeper";
        //其他入库单-单据类型
        public static final String OTHER_IN_STOCK_BILL_TYPE = "other_in_stock_bill_type";
        //其他入库单-供应商
        public static final String OTHER_IN_STOCK_SUPPLIER = "other_in_stock_supplier";
        //其他入库单-入库类型
        public static final String OTHER_IN_STOCK_TYPE = "other_in_stock_type";
        //其他入库单-项目(表头)
        public static final String OTHER_IN_STOCK_PROJECT_HEAD = "other_in_stock_project_head";
        //其他入库单-默认仓库
        public static final String OTHER_IN_STOCK_DEFAULT_STOCK = "other_in_stock_default_stock";
        //其他入库单-默认库存状态
        public static final String OTHER_IN_STOCK_DEFAULT_STATUS = "other_in_stock_default_status";
        //其他出库单-单据类型
        public static final String OTHER_OUT_STOCK_BILL_TYPE = "other_out_stock_bill_type";
        //其他出库单-客户
        public static final String OTHER_OUT_STOCK_CUSTOMER = "other_out_stock_customer";
        //其他出库单-领料部门
        public static final String OTHER_OUT_STOCK_DEPT = "other_out_stock_dept";
        //其他出库单-领料人
        public static final String OTHER_OUT_STOCK_PICKER = "other_out_stock_picker";
        //其他出库单-业务类型
        public static final String OTHER_OUT_STOCK_BIZ_TYPE = "other_out_stock_biz_type";
        //其他出库单-业务类型(助手)
        public static final String OTHER_OUT_STOCK_ASSISTANT = "other_out_stock_assistant";
        //其他出库单-产品名称
        public static final String OTHER_OUT_STOCK_PRODUCT = "other_out_stock_product";
        //其他出库单-默认仓库
        public static final String OTHER_OUT_STOCK_DEFAULT_STOCK = "other_out_stock_default_stock";
        //其他出库单-默认库存状态
        public static final String OTHER_OUT_STOCK_DEFAULT_STATUS = "other_out_stock_default_status";
        //调拨单-调出仓库
        public static final String TRANSFER_SRC_STOCK = "transfer_src_stock";
        //调拨单-调入仓库
        public static final String TRANSFER_DEST_STOCK = "transfer_dest_stock";
        //调拨单-调出库位区域
        public static final String TRANSFER_SRC_STOCK_LOC_AREA = "transfer_src_stock_loc_area";
        //调拨单-调出库位位置
        public static final String TRANSFER_SRC_STOCK_LOC_POSITION = "transfer_src_stock_loc_position";
        //调拨单-调入库位区域
        public static final String TRANSFER_DEST_STOCK_LOC_AREA = "transfer_dest_stock_loc_area";
        //调拨单-调入库位位置
        public static final String TRANSFER_DEST_STOCK_LOC_POSITION = "transfer_dest_stock_loc_position";
        //调拨单-供应商
        public static final String TRANSFER_SUPPLIER_ID = "transfer_supplier_id";
        //调拨单-默认库存状态
        public static final String TRANSFER_DEFAULT_STOCK_STATUS = "transfer_default_stock_status";
        //采购入库单-默认组织
        public static final String DEFAULT_ORG_ID = "default_org_id";
        //采购入库单-默认部门
        public static final String DEFAULT_DEPT_ID = "default_dept_id";

        //采购入库单-默认仓库
        public static final String DEFAULT_STOCK_ID = "default_stock_id";

        //ERP基础数据同步开关
        public static final String ERP_BASIC_DATA_SYNC_ENABLED = "ERP_BASIC_DATA_SYNC_ENABLED";
        //ERP单据同步开关
        public static final String ERP_BILL_SYNC_ENABLED = "ERP_BILL_SYNC_ENABLED";
    }

    /**
     * ERP查询单据信息常量
     */
    public static class ErpQueryContext {
        public static final String ERP_MATERIAL = "物料信息";
        public static final String ERP_MATERIAL_GROUP = "物料分组";
        public static final String ERP_PRODUCTION_PICKING_MATERIAL = "生产领料单";
        public static final String ERP_PRODUCTION_FEED_MATERIAL = "生产补料单";
        public static final String ERP_PRODUCTION_IN_STOCK = "生产入库单";
        public static final String ERP_SIMPLE_PRODUCTION_PICKING_MATERIAL = "简单生产领料单";
        public static final String ERP_SIMPLE_PRODUCTION_IN_STOCK = "简单生产入库单";
        public static final String ERP_OTHER_IN_STOCK = "其他入库单";
        public static final String ERP_OTHER_OUT_STOCK = "其他出库单";
        public static final String ERP_RECEIVE_NOTICE = "收料通知单";
        public static final String ERP_RETURN_MATERIAL = "退料申请单";
        public static final String ERP_RETURN_NOTICE = "退货通知单";
    }

    /**
     * ERP接口查询的表单ID
     */
    public static class ErpFormId {
        /** 物料 */
        public static final String MATERIAL = "BD_MATERIAL";
        /** 物料分组 */
        public static final String MATERIAL_GROUP = "SAL_MATERIALGROUP";
        /** 客户 */
        public static final String CUSTOMER = "BD_Customer";
        /** 供应商 */
        public static final String SUPPLIER = "BD_Supplier";
        /** 客户分组 */
        public static final String CUSTOMER_GROUP = "AMB_CustomerGroup";
        /** 供应商分组 */
        public static final String SUPPLIER_GROUP = "BD_SupplierGroup";
        /** 联系人 */
        public static final String COMMON_CONTACT = "BD_CommonContact";
        /** 计量单位 */
        public static final String UNIT = "BD_UNIT";
        /** 采购订单 */
        public static final String PURCHASE_ORDER = "PUR_PurchaseOrder";
        /** 采购入库单 */
        public static final String PURCHASE_IN_STOCK = "STK_InStock";
        /** 发货通知单 */
        public static final String DELIVERY_NOTICE = "SAL_DELIVERYNOTICE";
        /** 生产领料单 */
        public static final String PRODUCTION_PICKING_MATERIAL = "PRD_PickMtrl";
        /** 生产补料单 */
        public static final String PRODUCTION_FEED_MATERIAL = "PRD_FeedMtrl";
        /** 生产入库单 */
        public static final String PRODUCTION_IN_STOCK = "PRD_INSTOCK";
        /** 简单生产领料单 */
        public static final String SIMPLE_PRODUCTION_PICKING_MATERIAL = "SP_PickMtrl";
        /** 简单生产入库单 */
        public static final String SIMPLE_PRODUCTION_IN_STOCK = "SP_InStock";
        /** 其他入库单 */
        public static final String OTHER_IN_STOCK = "STK_MISCELLANEOUS";
        /** 其他出库单 */
        public static final String OTHER_OUT_STOCK = "STK_MisDelivery";
        /** 收料通知单 */
        public static final String RECEIVE_NOTICE = "PUR_ReceiveBill";
        /** 退料申请单 */
        public static final String RETURN_MATERIAL = "PUR_MRAPP";
        /** 采购退料单 */
        public static final String PURCHASE_RETURN_BILL = "PUR_MRB";
        /** 销售退货单 */
        public static final String SALE_RETURN_BILL = "SAL_RETURNSTOCK";
        /** 退货通知单 */
        public static final String RETURN_NOTICE = "SAL_RETURNNOTICE";
    }

    /**
     * ERP查询字段常量
     */
    public static final class ErpFieldKeys {
        /** 通用ID */
        public static final String ID = "Fid";
        /** 通用编码 */
        public static final String CODE = "FNumber";
        /** 通用名称 */
        public static final String NAME = "FName";
        /** 通用描述 */
        public static final String DESCRIPTION = "FDescription";

        /**
         * 物料
         */
        public static final class Material {
            /** 物料ID */
            public static final String ID = "FMATERIALID";
            /** 物料名称 */
            public static final String NAME = ErpFieldKeys.NAME;
            /** 物料编码 */
            public static final String CODE = ErpFieldKeys.CODE;
            /** 物料分组/物料类别 */
            public static final String GROUP = "FMaterialGroup";
            /** 物料属性 */
            public static final String BASE_PROPERTY = "FBaseProperty";
            /** 规格型号 */
            public static final String SPECIFICATION = "FSpecification";
            /** 基本单位 */
            public static final String BASE_UNIT = "FBaseUnitId";
            /** 采购单位 */
            public static final String PURCHASE_UNIT = "FPurchaseUnitId";
            /** 生产单位 */
            public static final String PRODUCE_UNIT = "FProduceUnitId";
            /** 物料类别 */
            public static final String ERP_CLS_ID = "FErpClsID";
            /** 允许委外 */
            public static final String IS_SUB_CONTRACT = "FIsSubContract";
            /** 允许生产 */
            public static final String IS_PRODUCE = "FIsProduce";
            /** 允许采购 */
            public static final String IS_PURCHASE = "FIsPurchase";
        }

        /**
         * 物料分组
         */
        public static final class MaterialGroup {
            /** 分组ID */
            public static final String ID = "Fid";
            /** 分组编码 */
            public static final String CODE = ErpFieldKeys.CODE;
            /** 父级ID */
            public static final String PARENT_ID = "FParentid";
            /** 分组名称 */
            public static final String NAME = ErpFieldKeys.NAME;
        }

        /**
         * 客户
         */
        public static final class Customer {
        /** 客户ID */
            public static final String ID = "FCUSTID";
            /** 客户名称 */
            public static final String NAME = ErpFieldKeys.NAME;
            /** 客户编码 */
            public static final String CODE = ErpFieldKeys.CODE;
            /** 描述 */
            public static final String DESCRIPTION = ErpFieldKeys.DESCRIPTION;
            /** 客户分组 */
            public static final String GROUP = "FGROUP";
        }

        /**
         * 供应商
         */
        public static final class Supplier {
        /** 供应商ID */
            public static final String ID = "FSupplierId";
            /** 供应商名称 */
            public static final String NAME = ErpFieldKeys.NAME;
            /** 供应商编码 */
            public static final String CODE = ErpFieldKeys.CODE;
            /** 描述 */
            public static final String DESCRIPTION = ErpFieldKeys.DESCRIPTION;
            /** 供应商分组 */
            public static final String GROUP = "FGROUP";
        }

        /**
         * 客户分组
         */
        public static final class CustomerGroup {
            /** 分组ID */
            public static final String ID = ErpFieldKeys.ID;
            /** 分组编码 */
            public static final String CODE = ErpFieldKeys.CODE;
            /** 分组名称 */
            public static final String NAME = ErpFieldKeys.NAME;
        }

        /**
         * 供应商分组
         */
        public static final class SupplierGroup {
            /** 分组ID */
            public static final String ID = ErpFieldKeys.ID;
            /** 分组编码 */
            public static final String CODE = ErpFieldKeys.CODE;
            /** 分组名称 */
            public static final String NAME = ErpFieldKeys.NAME;
        }

        /**
         * 联系人
         */
        public static final class Contact {
            /** 联系人ID */
            public static final String ID = "FCONTACTID";
            /** 姓名 */
            public static final String NAME = ErpFieldKeys.NAME;
            /** 编码 */
            public static final String CODE = ErpFieldKeys.CODE;
            /** 描述 */
            public static final String DESCRIPTION = ErpFieldKeys.DESCRIPTION;
            /** 职务 */
            public static final String POSITION = "FPost";
            /** 移动电话 */
            public static final String MOBILE = "FMobile";
            /** 邮箱 */
            public static final String EMAIL = "FEmail";
            /** 详细地址 */
            public static final String ADDRESS = "FBizAddress";
            /** 所属客户ID */
            public static final String CUSTOMER_ID = "FCustId";
            /** 所属公司 */
            public static final String COMPANY = "FCompany";
        }

        /**
         * 计量单位
         */
        public static final class Unit {
            /** 实体主键 */
            public static final String ID = "FUNITID";
            /** 名称 */
            public static final String NAME = ErpFieldKeys.NAME;
            /** 编码 */
            public static final String CODE = ErpFieldKeys.CODE;
            /** 描述 */
            public static final String DESCRIPTION = ErpFieldKeys.DESCRIPTION;
            /** 是否基准计量单位 */
            public static final String IS_BASE_UNIT = "FIsBaseUnit";
        }

        /**
         * 采购订单
         */
        public static final class PurchaseOrder {
            /**
             * 采购订单 - 表头
             */
            public static final class Head {
                /** 实体主键 */
                public static final String ID = "FID";
                /** 单据编号 */
                public static final String BILL_NO = "FBillNo";
                /** 采购日期 */
                public static final String DATE = "FDate";
                /** 供应商 */
        public static final String SUPPLIER_ID = "FSupplierId";
                /** 采购部门 */
                public static final String PURCHASE_DEPT_ID = "FPurchaseDeptId";
                /** 采购员 */
                public static final String PURCHASER_ID = "FPurchaserId";
                /** 创建人 */
                public static final String CREATOR_ID = "FCreatorId";
                /** 业务类型 */
                public static final String BUSINESS_TYPE = "FBusinessType";
                /** 供货方  直接对应客户表里面的ID*/
                public static final String PROVIDER_ID = "FProviderId";
                /** 供货方联系人 */
                public static final String PROVIDER_CONTACT_ID = "FProviderContactId";
                /** 结算方 */
                public static final String SETTLE_ID = "FSettleId";
                /** 收款方 */
                public static final String CHARGE_ID = "FChargeId";
            }

            /**
             * 采购订单 - 物料明细
             */
            public static final class Entry {
                /** 实体主键 */
                public static final String ENTRY_ID = "FPOOrderEntry_FEntryID";
                /** 物料编码   对应物料基础表里面的物料编码*/
                public static final String MATERIAL_ID = "FMaterialId.FNumber";
                /** 采购单位 */
                public static final String UNIT_ID = "FUnitId";
                /** 计价单位 */
                public static final String PRICE_UNIT_ID = "FPriceUnitId";
                /** 采购数量 */
                public static final String QTY = "FQty";
                /** 产品类型 */
                public static final String ROW_TYPE = "FRowType";
                /** 备注 */
                public static final String ENTRY_NOTE = "FEntryNote";
                /** 交货日期 */
                public static final String DELIVERY_DATE = "FDeliveryDate";
                /** 库存单位 */
                public static final String STOCK_UNIT_ID = "FStockUnitID";
            }
        }

        /**
         * 发货通知单
         */
        public static final class DeliveryNotice {
            /**
             * 发货通知单 - 表头
             */
            public static final class Head {
                /** 实体主键 */
                public static final String ID = "FID";
                /** 单据编号 */
                public static final String BILL_NO = "FBillNo";
                /** 销售员 */
                public static final String SALESMAN_ID = "FSalesManID";
                /** 销售部门 */
                public static final String SALE_DEPT_ID = "FSaleDeptID";
                /** 客户 */
                public static final String CUSTOMER_ID = "FCustomerID";
                /** 交货方式 */
                public static final String DELIVERY_WAY = "FHeadDeliveryWay";
                /** 日期 */
                public static final String DATE = "FDate";
                /** 结算币别 */
                public static final String SETTLE_CURR_ID = "FSettleCurrID";
                /** 仓管员 */
                public static final String STOCKER_ID = "FStockerID";
                /** 收货方 */
                public static final String RECEIVER_ID = "FReceiverID";
                /** 结算方 */
                public static final String SETTLE_ID = "FSettleID";
                /** 收货方联系人 */
                public static final String RECEIVER_CONTACT_ID = "FReceiverContactID";
                /** 付款方 */
                public static final String PAYER_ID = "FPayerID";
                /** 收货方地址 */
                public static final String RECEIVE_ADDRESS = "FReceiveAddress";
            }

            /**
             * 发货通知单 - 明细
             */
            public static final class Entry {
                /** 实体主键 */
                public static final String ENTRY_ID = "FEntity_FEntryID";
                /** 物料编码 */
                public static final String MATERIAL_CODE = "FMaterialID.FNumber";
                /** 父项物料 */
                public static final String PARENT_MATERIAL_ID = "FParentMatId";
                /** 销售单位 */
                public static final String UNIT_ID = "FUnitID";
                /** 销售数量 */
                public static final String QTY = "FQty";
                /** 交货日期 */
                public static final String DELIVERY_DATE = "FDeliveryDate";
                /** 物料类别 */
                public static final String MATERIAL_TYPE = "FMateriaType";
            }
        }

        /**
         * 生产领料单
         */
        public static final class ProductionPickingMaterial {
            /**
             * 生产领料单 - 表头
             */
            public static final class Head {
                /** 实体主键 */
                public static final String ID = "FID";
                /** 单据编号 */
                public static final String BILL_NO = "FBillNo";
                /** 备注 */
                public static final String DESCRIPTION = "FDescription";
                /** 日期 */
                public static final String DATE = "FDate";
                /** 生产组织 */
                public static final String PRD_ORG_ID = "FPrdOrgId";
                /** 发料组织 */
                public static final String STOCK_ORG_ID = "FStockOrgId";
            }

            /**
             * 生产领料单 - 明细
             */
            public static final class Entry {
                /** 实体主键 */
                public static final String ENTRY_ID = "FEntity_FEntryID";
                /** 物料编码 */
                public static final String MATERIAL_ID = "FMaterialId.FNumber";
                /** 物料名称 */
                public static final String MATERIAL_NAME = "FMaterialName";
                /** 规格型号 */
                public static final String SPECIFICATION = "FSpecification";
                /** 仓库 */
                public static final String STOCK_ID = "FStockId";
                /** 仓位 */
                public static final String STOCK_LOC_ID = "FStockLocId";
                /** 库存状态 */
                public static final String STOCK_STATUS_ID = "FStockStatusId";
                /** 生产订单编号 */
                public static final String MO_BILL_NO = "FMoBillNo";
                /** 生产订单分录内码 */
                public static final String MO_ENTRY_ID = "FMoEntryId";
                /** 用料清单分录内码 */
                public static final String PP_BOM_ENTRY_ID = "FPPBomEntryId";
                /** 货主类型 */
                public static final String OWNER_TYPE_ID = "FOwnerTypeId";
                /** 申请数量 */
                public static final String APP_QTY = "FAppQty";
                /** 实发数量 */
                public static final String ACTUAL_QTY = "FActualQty";
                /** 备注 */
                public static final String ENTRY_MEMO = "FEntrtyMemo";
                /** 生产订单内码 */
                public static final String MO_ID = "FMoId";
                /** 生产订单行号 */
                public static final String MO_ENTRY_SEQ = "FMoEntrySeq";
                /** 单位 */
                public static final String UNIT_ID = "FUnitID";
                /** 基本单位 */
                public static final String BASE_UNIT_ID = "FBaseUnitId";
                /** 主库存单位 */
                public static final String STOCK_UNIT_ID = "FStockUnitId";
                /** 保管者类型 */
                public static final String KEEPER_TYPE_ID = "FKeeperTypeId";
                /** 保管者 */
                public static final String KEEPER_ID = "FKeeperId";
                /** 货主 */
                public static final String OWNER_ID = "FOwnerId";
                /** 产品货主类型 */
                public static final String PARENT_OWNER_TYPE_ID = "FParentOwnerTypeId";
                /** 产品货主 */
                public static final String PARENT_OWNER_ID = "FParentOwnerId";
                /** 车间 */
                public static final String ENTRY_WORKSHOP_ID = "FEntryWorkShopId";
            }
        }
        /**
         * 生产补料单
         */
        public static final class ProductionFeedMaterial {
            /**
             * 生产补料单 - 表头
             */
            public static final class Head {
                /** 实体主键 */
                public static final String ID = "FID";
                /** 单据编号 */
                public static final String BILL_NO = "FBillNo";
                /** 备注 */
                public static final String DESCRIPTION = "FDescription";
                /** 日期 */
                public static final String DATE = "FDate";
                /** 生产组织 */
                public static final String PRD_ORG_ID = "FPrdOrgId";
                /** 发料组织 */
                public static final String STOCK_ORG_ID = "FStockOrgId";
            }

            /**
             * 生产补料单 - 明细
             */
            public static final class Entry {
                /** 实体主键 */
                public static final String ENTRY_ID = "FEntity_FEntryID";
                /** 仓库 */
                public static final String STOCK_ID = "FStockId";
                /** 物料编码 */
                public static final String MATERIAL_ID = "FMaterialId.FNumber";
                /** 物料名称 */
                public static final String MATERIAL_NAME = "FMaterialName";
                /** 规格型号 */
                public static final String SPECIFICATION = "FSpecification";
                /** 仓位 */
                public static final String STOCK_LOC_ID = "FStockLocId";
                /** 生产日期 */
                public static final String PRODUCE_DATE = "FProduceDate";
                /** 生产订单编号 */
                public static final String MO_BILL_NO = "FMoBillNo";
                /** 生产订单分录内码 */
                public static final String MO_ENTRY_ID = "FMoEntryId";
                /** 用料清单分录内码 */
                public static final String PP_BOM_ENTRY_ID = "FPPBomEntryId";
                /** 货主类型 */
                public static final String OWNER_TYPE_ID = "FOwnerTypeId";
                /** 申请数量 */
                public static final String APP_QTY = "FAppQty";
                /** 实发数量 */
                public static final String ACTUAL_QTY = "FActualQty";
                /** 备注 */
                public static final String ENTRY_DESCRIPTION = "FEntrtyDescription";
                /** 报废数量 */
                public static final String SCRAP_QTY = "FScrapQty";
                /** 车间 */
                public static final String ENTRY_WORKSHOP_ID = "FEntryWorkShopId";
                /** 保管者类型 */
                public static final String KEEPER_TYPE_ID = "FKeeperTypeId";
                /** 保管者 */
                public static final String KEEPER_ID = "FKeeperId";
                /** 货主 */
                public static final String OWNER_ID = "FOwnerId";
                /** 系统源单类型 */
                public static final String ENTRY_SRC_BILL_TYPE = "FEntrySrcBillType";
                /** 系统源单编号 */
                public static final String ENTRY_SRC_BILL_NO = "FEntrySrcBillNo";
                /** 产品货主类型 */
                public static final String PARENT_OWNER_TYPE_ID = "FParentOwnerTypeId";
                /** 产品货主 */
                public static final String PARENT_OWNER_ID = "FParentOwnerId";
                /** 单位 */
                public static final String UNIT_ID = "FUnitID";
                /** 基本单位 */
                public static final String BASE_UNIT_ID = "FBaseUnitId";
                /** 主库存单位 */
                public static final String STOCK_UNIT_ID = "FStockUnitId";
            }
        }
        /**
         * 生产入库单
         */
        public static final class ProductionInStock {
            /**
             * 生产入库单 - 表头
             */
            public static final class Head {
                /** 实体主键 */
                public static final String ID = "FID";
                /** 单据编号 */
                public static final String BILL_NO = "FBillNo";
                /** 备注 */
                public static final String DESCRIPTION = "FDescription";
                /** 日期 */
                public static final String DATE = "FDate";
                /** 生产组织 */
                public static final String PRD_ORG_ID = "FPrdOrgId";
                /** 入库组织 */
                public static final String STOCK_ORG_ID = "FStockOrgId";
                /** 货主 */
                public static final String OWNER_ID_0 = "FOwnerId0";
            }

            /**
             * 生产入库单 - 明细
             */
            public static final class Entry {
                /** 实体主键 */
                public static final String ENTRY_ID = "FEntity_FEntryID";
                /** 物料编码 */
                public static final String MATERIAL_ID = "FMaterialId.FNumber";
                /** 物料名称 */
                public static final String MATERIAL_NAME = "FMaterialName";
                /** 规格型号 */
                public static final String SPECIFICATION = "FSpecification";
                /** 产品类型 */
                public static final String PRODUCT_TYPE = "FProductType";
                /** 入库类型 */
                public static final String IN_STOCK_TYPE = "FInStockType";
                /** 单位 */
                public static final String UNIT_ID = "FUnitID";
                /** 基本单位 */
                public static final String BASE_UNIT_ID = "FBaseUnitId";
                /** 应收数量 */
                public static final String MUST_QTY = "FMustQty";
                /** 基本单位应收数量 */
                public static final String BASE_MUST_QTY = "FBaseMustQty";
                /** 实收数量 */
                public static final String REAL_QTY = "FRealQty";
                /** 基本单位库存实收数量 */
                public static final String BASE_REAL_QTY = "FBaseRealQty";
                /** 货主类型 */
                public static final String OWNER_TYPE_ID = "FOwnerTypeId";
                /** 货主 */
                public static final String OWNER_ID = "FOwnerId";
                /** 仓库 */
                public static final String STOCK_ID = "FStockId";
                /** 生产订单编号 */
                public static final String MO_BILL_NO = "FMoBillNo";
                /** 生产订单内码 */
                public static final String MO_ID = "FMoId";
                /** 生产订单分录内码 */
                public static final String MO_ENTRY_ID = "FMoEntryId";
                /** 生产订单行号 */
                public static final String MO_ENTRY_SEQ = "FMoEntrySeq";
                /** 备注 */
                public static final String MEMO = "FMemo";
                /** 库存状态 */
                public static final String STOCK_STATUS_ID = "FStockStatusId";
                /** 保管者类型 */
                public static final String KEEPER_TYPE_ID = "FKeeperTypeId";
                /** 保管者 */
                public static final String KEEPER_ID = "FKeeperId";
            }
        }

        /**
         * 简单生产领料单
         */
        public static final class SimpleProductionPickingMaterial {
            /**
             * 简单生产领料单 - 表头
             */
            public static final class Head {
                /** 实体主键 */
                public static final String ID = "FID";
                /** 单据编号 */
                public static final String BILL_NO = "FBillNo";
                /** 备注 */
                public static final String DESCRIPTION = "FDescription";
                /** 日期 */
                public static final String DATE = "FDate";
                /** 发料组织 */
                public static final String STOCK_ORG_ID = "FStockOrgId";
                /** 生产组织 */
                public static final String PRD_ORG_ID = "FPrdOrgId";
                /** 货主类型 */
                public static final String OWNER_TYPE_ID = "FOwnerTypeId0";
                /** 生产车间 */
                public static final String WORKSHOP_ID = "FWorkShopId";
            }

            /**
             * 简单生产领料单 - 明细
             */
            public static final class Entry {
                /** 实体主键 */
                public static final String ENTRY_ID = "FEntity_FEntryID";
                /** 物料编码 */
                public static final String MATERIAL_ID = "FMaterialId.FNumber";
                /** 物料名称 */
                public static final String MATERIAL_NAME = "FMaterialName";
                /** 规格型号 */
                public static final String SPECIFICATION = "FSpecification";
                /** 仓库 */
                public static final String STOCK_ID = "FStockId";
                /** 库存状态 */
                public static final String STOCK_STATUS_ID = "FStockStatusId";
                /** 货主类型 */
                public static final String OWNER_TYPE_ID = "FOwnerTypeId";
                /** 申请数量 */
                public static final String APP_QTY = "FAppQty";
                /** 实发数量 */
                public static final String ACTUAL_QTY = "FActualQty";
                /** 备注 */
                public static final String ENTRY_MEMO = "FEntrtyMemo";
                /** 单位 */
                public static final String UNIT_ID = "FUnitID";
                /** 基本单位 */
                public static final String BASE_UNIT_ID = "FBaseUnitId";
                /** 主库存单位 */
                public static final String STOCK_UNIT_ID = "FStockUnitId";
                /** 保管者类型 */
                public static final String KEEPER_TYPE_ID = "FKeeperTypeId";
                /** 保管者 */
                public static final String KEEPER_ID = "FKeeperId";
                /** 货主 */
                public static final String OWNER_ID = "FOwnerId";
            }
        }

        /**
         * 简单生产入库单
         */
        public static final class SimpleProductionInStock {
            /**
             * 简单生产入库单 - 表头
             */
            public static final class Head {
                /** 实体主键 */
                public static final String ID = "FID";
                /** 单据编号 */
                public static final String BILL_NO = "FBillNo";
                /** 备注 */
                public static final String DESCRIPTION = "FDescription";
                /** 日期 */
                public static final String DATE = "FDate";
                /** 入库组织 */
                public static final String STOCK_ORG_ID = "FStockOrgId";
                /** 生产组织 */
                public static final String PRD_ORG_ID = "FPrdOrgId";
                /** 货主类型 */
                public static final String OWNER_TYPE_ID = "FOwnerTypeId0";
            }

            /**
             * 简单生产入库单 - 明细
             */
            public static final class Entry {
                /** 实体主键 */
                public static final String ENTRY_ID = "FEntity_FEntryID";
                /** 物料编码 */
                public static final String MATERIAL_ID = "FMaterialId.FNumber";
                /** 物料名称 */
                public static final String MATERIAL_NAME = "FMaterialName";
                /** 规格型号 */
                public static final String SPECIFICATION = "FSpecification";
                /** 入库类型 */
                public static final String IN_STOCK_TYPE = "FInStockType";
                /** 单位 */
                public static final String UNIT_ID = "FUnitID";
                /** 基本单位 */
                public static final String BASE_UNIT_ID = "FBaseUnitId";
                /** 应收数量 */
                public static final String MUST_QTY = "FMustQty";
                /** 基本单位应收数量 */
                public static final String BASE_MUST_QTY = "FBaseMustQty";
                /** 实收数量 */
                public static final String REAL_QTY = "FRealQty";
                /** 基本单位实收数量 */
                public static final String BASE_REAL_QTY = "FBaseRealQty";
                /** 货主类型 */
                public static final String OWNER_TYPE_ID = "FOwnerTypeId";
                /** 货主 */
                public static final String OWNER_ID = "FOwnerId";
                /** 仓库 */
                public static final String STOCK_ID = "FStockId";
                /** 生产车间 */
                public static final String WORKSHOP_ID = "FWorkShopId1";
                /** 备注 */
                public static final String MEMO = "FMemo";
                /** 库存状态 */
                public static final String STOCK_STATUS_ID = "FStockStatusId";
                /** 保管者类型 */
                public static final String KEEPER_TYPE_ID = "FKeeperTypeId";
                /** 保管者 */
                public static final String KEEPER_ID = "FKeeperId";
            }
        }

        /**
         * 收料通知单
         */
        public static final class ReceiveNotice {
            /**
             * 收料通知单 - 表头
             */
            public static final class Head {
                /** 实体主键 */
                public static final String ID = "FID";
                /** 单据编号 */
                public static final String BILL_NO = "FBillNo";
                /** 收料组织 */
                public static final String STOCK_ORG_ID = "FStockOrgId";
                /** 收料日期 */
                public static final String DATE = "FDate";
                /** 单据类型 */
                public static final String BILL_TYPE_ID = "FBillTypeID";
                /** 货主类型 */
                public static final String OWNER_TYPE_ID_HEAD = "FOwnerTypeIdHead";
                /** 货主 */
                public static final String OWNER_ID_HEAD = "FOwnerIdHead";
                /** 供应商 */
                public static final String SUPPLIER_ID = "FSupplierId";
                /** 收料部门 */
                public static final String RECEIVE_DEPT_ID = "FReceiveDeptId";
                /** 采购组织 */
                public static final String PUR_ORG_ID = "FPurOrgId";
                /** 采购部门 */
                public static final String PUR_DEPT_ID = "FPurDeptId";
                /** 采购员 */
                public static final String PURCHASER_ID = "FPurchaserId";
                /** 备注 */
                public static final String NOTE = "FNote";
                /** 验收方式 */
                public static final String ACC_TYPE = "FACCTYPE";
            }

            /**
             * 收料通知单 - 明细
             */
            public static final class Entry {
                /** 实体主键 */
                public static final String ENTRY_ID = "FDetailEntity_FEntryID";
                /** 物料编码 */
                public static final String MATERIAL_ID = "FMaterialId.FNumber";
                /** 物料名称 */
                public static final String MATERIAL_NAME = "FMaterialName";
                /** 规格型号 */
                public static final String MATERIAL_MODEL = "FMateriaModel";
                /** 收料单位 */
                public static final String UNIT_ID = "FUnitId";
                /** 实到数量 */
                public static final String ACT_RECEIVE_QTY = "FActReceiveQty";
                /** 预计到货日期 */
                public static final String PRE_DELIVERY_DATE = "FPreDeliveryDate";
                /** 供应商交货数量 */
                public static final String SUP_DEL_QTY = "FSUPDELQTY";
                /** 计价单位 */
                public static final String PRICE_UNIT_ID = "FPriceUnitId";
                /** 计价数量 */
                public static final String PRICE_UNIT_QTY = "FPriceUnitQty";
                /** 仓库 */
                public static final String STOCK_ID = "FStockID";
                /** 仓位 */
                public static final String STOCK_LOC_ID = "FStockLocId";
                /** 库存单位 */
                public static final String STOCK_UNIT_ID = "FStockUnitID";
                /** 库存单位数量 */
                public static final String STOCK_QTY = "FStockQty";
                /** 订单数量 */
                public static final String PO_QTY = "FPOQTY";
            }
        }

        /**
         * 退料申请单
         */
        public static final class ReturnMaterial {
            /**
             * 退料申请单 - 表头
             */
            public static final class Head {
                /** 实体主键 */
                public static final String ID = "FID";
                /** 单据编号 */
                public static final String BILL_NO = "FBillNo";
                /** 单据类型 */
                public static final String BILL_TYPE_ID = "FBillTypeID";
                /** 申请日期 */
                public static final String DATE = "FDate";
                /** 采购组织 */
                public static final String PURCHASE_ORG_ID = "FPURCHASEORGID";
                /** 退料类型 */
                public static final String RM_TYPE = "FRMTYPE";
                /** 申请组织 */
                public static final String APP_ORG_ID = "FAPPORGID";
                /** 退料地点 */
                public static final String RM_LOC = "FRMLOC";
                /** 供应商 */
                public static final String SUPPLIER_ID = "FSUPPLIERID";
                /** 退料方式 */
                public static final String RM_MODE = "FRMMODE";
                /** 补料方式 */
                public static final String REPLENISH_MODE = "FREPLENISHMODE";
                /** 业务类型 */
                public static final String BUSINESS_TYPE = "FBusinessType";
                /** 备注 */
                public static final String REMARKS = "FRemarks";
                /** 退料原因 */
                public static final String RM_REASON = "FRMREASON";
                /** 申请人 */
                public static final String APPLICANT_ID = "FApplicantId";
                /** 申请部门 */
                public static final String APP_DEPT_ID = "FAPPDEPTID";
            }

            /**
             * 退料申请单 - 明细
             */
            public static final class Entry {
                /** 实体主键 */
                public static final String ENTRY_ID = "FEntity_FEntryID";
                /** 物料编码 */
                public static final String MATERIAL_ID = "FMATERIALID.FNumber";
                /** 物料名称 */
                public static final String MATERIAL_NAME = "FMaterialName";
                /** 规格型号 */
                public static final String UOM = "FUOM";
                /** 申请退料数量 */
                public static final String MR_APP_QTY = "FMRAPPQTY";
                /** 备注 */
                public static final String NOTE_M = "FNOTE_M";
                /** 库存单位 */
                public static final String UNIT_ID = "FUNITID";
                /** 计价单位 */
                public static final String PRICE_UNIT_ID_F = "FPRICEUNITID_F";
                /** 计价数量 */
                public static final String PRICE_QTY_F = "FPRICEQTY_F";
                /** 补料数量 */
                public static final String REPLENISH_QTY = "FREPLENISHQTY";
                /** 采购单位 */
                public static final String PUR_UNIT_ID = "FPURUNITID";
                /** 采购数量 */
                public static final String PUR_QTY = "FPurQty";
                /** 仓库 */
                public static final String STOCK_ID = "FStockId";
                /** 仓位 */
                public static final String STOCK_LOC_ID = "FSTOCKLOCID";
            }
        }

        /**
         * 退货通知单
         */
        public static final class ReturnNotice {
            /**
             * 退货通知单 - 表头
             */
            public static final class Head {
                /** 实体主键 */
                public static final String ID = "FID";
                /** 单据编号 */
                public static final String BILL_NO = "FBillNo";
                /** 日期 */
                public static final String DATE = "FDate";
                /** 销售组织 */
                public static final String SALE_ORG_ID = "FSaleOrgId";
                /** 库存组织 */
                public static final String RET_ORG_ID = "FRetorgId";
                /** 库存部门 */
                public static final String RET_DEPT_ID = "FRetDeptId";
                /** 库存组 */
                public static final String STOCKER_GROUP_ID = "FStockerGroupId";
                /** 仓管员 */
                public static final String STOCKER_ID = "FStockerId";
                /** 退货客户 */
                public static final String RET_CUST_ID = "FRetcustId";
                /** 单据类型 */
                public static final String BILL_TYPE_ID = "FBillTypeID";
                /** 业务类型 */
                public static final String BUSINESS_TYPE = "FBussinessType";
                /** 退货原因 */
                public static final String RETURN_REASON = "FReturnReason";
                /** 收货方地址 */
                public static final String RECEIVE_ADDRESS = "FReceiveAddress";
                /** 交货地点 */
                public static final String HEAD_LOC_ID = "FHeadLocId";
                /** 备注 */
                public static final String DESCRIPTION = "FDescription";
                /** 收货人姓名 */
                public static final String LINK_MAN = "FLinkMan";
                /** 联系电话 */
                public static final String LINK_PHONE = "FLinkPhone";
            }

            /**
             * 退货通知单 - 明细
             */
            public static final class Entry {
                /** 实体主键 */
                public static final String ENTRY_ID = "FEntity_FENTRYID";
                /** 物料编码 */
                public static final String MATERIAL_ID = "FMaterialId.FNumber";
                /** 物料名称 */
                public static final String MATERIAL_NAME = "FMaterialName";
                /** 规格型号 */
                public static final String MATERIAL_MODEL = "FMaterialModel";
                /** 销售数量 */
                public static final String QTY = "FQty";
                /** 基本单位 */
                public static final String BASE_UNIT_ID = "FASEUNITID";
                /** 备注 */
                public static final String ENTRY_DESCRIPTION = "FEntryDescription";
                /** 订单单号 */
                public static final String ORDER_NO = "FOrderNo";
                /** 批号 */
                public static final String LOT = "FLot";
                /** 退货类型 */
                public static final String RM_TYPE = "FRmType";
                /** 退货日期 */
                public static final String DELIVERY_DATE = "FDeliverydate";
                /** 销售单位 */
                public static final String UNIT_ID = "FUnitID";
            }
        }
    }


    /**
     * 客户/供应商 分组类型
     */
    public static class CompanyGroupType {
        public static final int CUSTOMER = 0; // 客户
        public static final int SUPPLIER = 1; // 供应商
    }

    /**
     * 来源单据类型（用于通用单据生成）
     */
    public static class SourceDocumentType {
        /** 采购订单 */
        public static final String PURCHASE_ORDER = "PURCHASE_ORDER";
        /** 发货通知单 */
        public static final String DELIVERY_NOTICE = "DELIVERY_NOTICE";
        /** 生产领料单 */
        public static final String PRODUCTION_PICKING_MATERIAL = "PRODUCTION_PICKING_MATERIAL";
        /** 生产补料单 */
        public static final String PRODUCTION_FEED_MATERIAL = "PRODUCTION_FEED_MATERIAL";
        /** 生产入库单 */
        public static final String PRODUCTION_IN_STOCK = "PRODUCTION_IN_STOCK";
        /** 简单生产领料单 */
        public static final String SIMPLE_PRODUCTION_PICKING_MATERIAL = "SIMPLE_PRODUCTION_PICKING_MATERIAL";
        /** 简单生产入库单 */
        public static final String SIMPLE_PRODUCTION_IN_STOCK = "SIMPLE_PRODUCTION_IN_STOCK";
    }


}
