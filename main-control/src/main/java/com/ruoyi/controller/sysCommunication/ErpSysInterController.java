package com.ruoyi.controller.sysCommunication;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.service.schedule.ErpSyncScheduleService;
import com.ruoyi.utils.ResponseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @version v1.0
 * @Description: [ERP系统接口控制交互]
 * @date 2024/9/18 17:55
 */
@RestController
@RequestMapping("/speedbot/sysCommunication/erySys")
public class ErpSysInterController extends BaseController {
    protected final Logger logger = LoggerFactory.getLogger(ErpSysInterController.class);

    @Resource
    private ErpSyncScheduleService erpSyncScheduleService;

    /**
     * 手动触发基础数据同步
     *
     * @return 执行结果
     */
    @PostMapping("/triggerBasicDataSync")
    public ResponseResult triggerBasicDataSync() {
        try {
            logger.info("收到手动触发基础数据同步请求");
            erpSyncScheduleService.syncBasicDataFromErp();
            return ResponseResult.getSuccessResult();
        } catch (Exception e) {
            logger.error("手动触发基础数据同步失败", e);
            return ResponseResult.getErrorResult("触发失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发ERP单据同步
     *
     * @return 执行结果
     */
    @PostMapping("/triggerBillDataSync")
    public ResponseResult triggerBillDataSync() {
        try {
            logger.info("收到手动触发ERP单据同步请求");
            erpSyncScheduleService.syncBillDataFromErp();
            return ResponseResult.getSuccessResult();
        } catch (Exception e) {
            logger.error("手动触发ERP单据同步失败", e);
            return ResponseResult.getErrorResult("触发失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发全量同步（基础数据 + ERP单据）
     *
     * @return 执行结果
     */
    @PostMapping("/triggerFullSync")
    public ResponseResult triggerFullSync() {
        try {
            logger.info("收到手动触发全量同步请求");
            // 先同步基础数据
            erpSyncScheduleService.syncBasicDataFromErp();
            // 等待30秒后同步单据数据
            Thread.sleep(30 * 1000);
            erpSyncScheduleService.syncBillDataFromErp();

            return ResponseResult.getSuccessResult();
        } catch (Exception e) {
            logger.error("手动触发全量同步失败", e);
            return ResponseResult.getErrorResult("触发失败: " + e.getMessage());
        }
    }

    /**
     * 获取同步状态
     *
     * @return 同步状态信息
     */
    @PostMapping("/getSyncStatus")
    public ResponseResult getSyncStatus() {
        try {
            logger.info("收到获取同步状态请求");
            Map<String, Object> syncStatus = erpSyncScheduleService.getSyncStatus();
            return ResponseResult.getSuccessResult("获取同步状态成功", syncStatus);
        } catch (Exception e) {
            logger.error("获取同步状态失败", e);
            return ResponseResult.getErrorResult("获取状态失败: " + e.getMessage());
        }
    }
}
